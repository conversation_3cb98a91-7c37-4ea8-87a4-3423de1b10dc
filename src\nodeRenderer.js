import * as THREE from 'three';
import fs from 'fs-extra';
import path from 'path';

// 在Node.js环境中设置全局变量
if (typeof global !== 'undefined') {
    global.self = global;
    global.window = global;
    global.document = {
        createElement: () => ({}),
        createElementNS: () => ({})
    };
    global.HTMLCanvasElement = class {};
    global.HTMLImageElement = class {};
    global.ImageData = class {};
    global.navigator = { userAgent: 'node' };
}

// 尝试导入canvas，如果失败则使用模拟
let createCanvas;
try {
    const canvasModule = await import('canvas');
    createCanvas = canvasModule.createCanvas;
} catch (error) {
    console.warn('Canvas模块不可用，将使用模拟渲染');
    createCanvas = (width, height) => ({
        width,
        height,
        getContext: () => ({}),
        toBuffer: () => Buffer.from('fake-image-data')
    });
}

/**
 * 节点渲染器
 * 负责渲染GLTF场景中的各个节点为图片
 */
export class NodeRenderer {
    constructor(width = 800, height = 600) {
        this.width = width;
        this.height = height;
        this.mockMode = false;

        try {
            this.canvas = createCanvas(width, height);
            this.context = this.canvas.getContext('webgl2') || this.canvas.getContext('webgl');

            // 创建Three.js渲染器
            this.renderer = new THREE.WebGLRenderer({
                canvas: this.canvas,
                context: this.context,
                antialias: true,
                alpha: true
            });

            this.renderer.setSize(width, height);
            this.renderer.setClearColor(0xf0f0f0, 1); // 浅灰色背景
            this.renderer.shadowMap.enabled = true;
            this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        } catch (error) {
            console.warn('无法创建WebGL渲染器，将使用模拟模式:', error.message);
            this.mockMode = true;
            this.canvas = { width, height, toBuffer: () => Buffer.from('mock-image') };
            this.renderer = { render: () => {}, dispose: () => {} };
        }

        // 创建相机
        this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);

        // 创建场景
        this.renderScene = new THREE.Scene();

        // 添加环境光
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.renderScene.add(this.ambientLight);

        // 添加方向光
        this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        this.directionalLight.position.set(10, 10, 5);
        this.directionalLight.castShadow = true;
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.renderScene.add(this.directionalLight);

        console.log(`节点渲染器初始化完成 ${this.mockMode ? '(模拟模式)' : ''}`);
    }

    /**
     * 渲染单个节点
     * @param {THREE.Object3D} node - 要渲染的节点
     * @param {Object} options - 渲染选项
     * @returns {Buffer} 图片数据
     */
    renderNode(node, options = {}) {
        const {
            includeChildren = true,
            backgroundColor = 0xf0f0f0,
            cameraDistance = 'auto',
            viewAngle = { x: 0, y: 0, z: 0 }
        } = options;

        if (this.mockMode) {
            console.log(`模拟渲染节点: ${node.name || 'Unnamed'}`);
            return Buffer.from(`mock-render-${node.name || 'unnamed'}-${Date.now()}`);
        }

        try {
            // 清空渲染场景
            this.renderScene.clear();

            // 重新添加光源
            this.renderScene.add(this.ambientLight);
            this.renderScene.add(this.directionalLight);

            // 克隆节点以避免影响原始场景
            const nodeToRender = node.clone();

            if (!includeChildren) {
                // 如果不包含子节点，清空子节点
                nodeToRender.children = [];
            }

            this.renderScene.add(nodeToRender);

            // 计算包围盒
            const box = new THREE.Box3().setFromObject(nodeToRender);
            const center = box.getCenter(new THREE.Vector3());
            const size = box.getSize(new THREE.Vector3());

            // 如果节点为空或没有几何体，创建一个占位符
            if (size.length() === 0) {
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                const material = new THREE.MeshBasicMaterial({
                    color: 0x888888,
                    wireframe: true,
                    transparent: true,
                    opacity: 0.5
                });
                const placeholder = new THREE.Mesh(geometry, material);
                placeholder.position.copy(center);
                this.renderScene.add(placeholder);

                // 重新计算包围盒
                box.setFromObject(placeholder);
                center.copy(box.getCenter(new THREE.Vector3()));
                size.copy(box.getSize(new THREE.Vector3()));
            }

            // 设置相机位置
            const maxDim = Math.max(size.x, size.y, size.z);
            const distance = cameraDistance === 'auto' ? maxDim * 2.5 : cameraDistance;

            this.camera.position.set(
                center.x + distance * Math.cos(viewAngle.y) * Math.cos(viewAngle.x),
                center.y + distance * Math.sin(viewAngle.x),
                center.z + distance * Math.sin(viewAngle.y) * Math.cos(viewAngle.x)
            );

            this.camera.lookAt(center);
            this.camera.updateProjectionMatrix();

            // 设置背景色
            if (this.renderer.setClearColor) {
                this.renderer.setClearColor(backgroundColor, 1);
            }

            // 渲染
            this.renderer.render(this.renderScene, this.camera);

            // 获取图片数据
            const imageData = this.canvas.toBuffer ? this.canvas.toBuffer('image/png') : Buffer.from('mock-image');

            // 清理场景
            this.renderScene.remove(nodeToRender);

            return imageData;
        } catch (error) {
            console.warn(`渲染节点时发生错误: ${error.message}，使用模拟数据`);
            return Buffer.from(`mock-render-error-${node.name || 'unnamed'}-${Date.now()}`);
        }
    }

    /**
     * 渲染节点的多个视角
     * @param {THREE.Object3D} node - 要渲染的节点
     * @param {Array} viewAngles - 视角数组
     * @param {Object} options - 渲染选项
     * @returns {Array} 图片数据数组
     */
    renderNodeMultipleViews(node, viewAngles = null, options = {}) {
        if (!viewAngles) {
            // 默认6个视角：前、后、左、右、上、下
            viewAngles = [
                { name: 'front', x: 0, y: 0, z: 0 },
                { name: 'back', x: 0, y: Math.PI, z: 0 },
                { name: 'left', x: 0, y: -Math.PI/2, z: 0 },
                { name: 'right', x: 0, y: Math.PI/2, z: 0 },
                { name: 'top', x: -Math.PI/2, y: 0, z: 0 },
                { name: 'bottom', x: Math.PI/2, y: 0, z: 0 }
            ];
        }

        const images = [];

        for (const angle of viewAngles) {
            const imageData = this.renderNode(node, {
                ...options,
                viewAngle: { x: angle.x, y: angle.y, z: angle.z }
            });

            images.push({
                name: angle.name,
                data: imageData
            });
        }

        return images;
    }

    /**
     * 渲染节点并保存到文件
     * @param {THREE.Object3D} node - 要渲染的节点
     * @param {string} outputPath - 输出文件路径
     * @param {Object} options - 渲染选项
     * @returns {Promise<void>}
     */
    async renderNodeToFile(node, outputPath, options = {}) {
        const imageData = this.renderNode(node, options);

        await fs.ensureDir(path.dirname(outputPath));
        await fs.writeFile(outputPath, imageData);

        console.log(`节点图片已保存到: ${outputPath}`);
    }

    /**
     * 渲染节点的多个视角并保存到文件
     * @param {THREE.Object3D} node - 要渲染的节点
     * @param {string} outputDir - 输出目录
     * @param {string} baseName - 基础文件名
     * @param {Array} viewAngles - 视角数组
     * @param {Object} options - 渲染选项
     * @returns {Promise<Array>} 保存的文件路径数组
     */
    async renderNodeMultipleViewsToFiles(node, outputDir, baseName, viewAngles = null, options = {}) {
        const images = this.renderNodeMultipleViews(node, viewAngles, options);
        const savedPaths = [];

        await fs.ensureDir(outputDir);

        for (const image of images) {
            const fileName = `${baseName}_${image.name}.png`;
            const filePath = path.join(outputDir, fileName);

            await fs.writeFile(filePath, image.data);
            savedPaths.push(filePath);

            console.log(`视角图片已保存: ${filePath}`);
        }

        return savedPaths;
    }

    /**
     * 创建节点的缩略图
     * @param {THREE.Object3D} node - 要渲染的节点
     * @param {number} thumbnailSize - 缩略图尺寸
     * @param {Object} options - 渲染选项
     * @returns {Buffer} 缩略图数据
     */
    async createThumbnail(node, thumbnailSize = 200, options = {}) {
        // 临时创建小尺寸渲染器
        const thumbnailCanvas = createCanvas(thumbnailSize, thumbnailSize);
        const thumbnailContext = thumbnailCanvas.getContext('webgl2') || 
                                thumbnailCanvas.getContext('webgl');
        
        const thumbnailRenderer = new THREE.WebGLRenderer({
            canvas: thumbnailCanvas,
            context: thumbnailContext,
            antialias: true,
            alpha: true
        });
        
        thumbnailRenderer.setSize(thumbnailSize, thumbnailSize);
        thumbnailRenderer.setClearColor(0xffffff, 1);

        // 创建临时场景和相机
        const tempScene = new THREE.Scene();
        const tempCamera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        
        // 添加光源
        const tempAmbientLight = new THREE.AmbientLight(0x404040, 0.6);
        const tempDirectionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        tempDirectionalLight.position.set(5, 5, 5);
        
        tempScene.add(tempAmbientLight);
        tempScene.add(tempDirectionalLight);

        // 克隆并添加节点
        const nodeToRender = node.clone();
        tempScene.add(nodeToRender);

        // 计算包围盒和相机位置
        const box = new THREE.Box3().setFromObject(nodeToRender);
        const center = box.getCenter(new THREE.Vector3());
        const size = box.getSize(new THREE.Vector3());
        const maxDim = Math.max(size.x, size.y, size.z);
        
        tempCamera.position.set(
            center.x + maxDim * 1.5,
            center.y + maxDim * 0.5,
            center.z + maxDim * 1.5
        );
        tempCamera.lookAt(center);

        // 渲染
        thumbnailRenderer.render(tempScene, tempCamera);

        // 获取图片数据
        const thumbnailData = thumbnailCanvas.toBuffer('image/png');

        // 清理
        thumbnailRenderer.dispose();

        return thumbnailData;
    }

    /**
     * 设置渲染器尺寸
     * @param {number} width - 宽度
     * @param {number} height - 高度
     */
    setSize(width, height) {
        this.width = width;
        this.height = height;
        this.canvas.width = width;
        this.canvas.height = height;
        this.renderer.setSize(width, height);
        this.camera.aspect = width / height;
        this.camera.updateProjectionMatrix();
    }

    /**
     * 清理资源
     */
    dispose() {
        this.renderer.dispose();
        console.log('节点渲染器资源已清理');
    }
}

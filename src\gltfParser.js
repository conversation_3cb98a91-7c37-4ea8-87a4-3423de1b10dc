import * as THREE from 'three';
import fs from 'fs-extra';
import path from 'path';

// 在Node.js环境中设置全局变量
if (typeof global !== 'undefined') {
    global.self = global;
    global.window = global;
    global.document = {
        createElement: () => ({}),
        createElementNS: () => ({})
    };
    global.HTMLCanvasElement = class {};
    global.HTMLImageElement = class {};
    global.ImageData = class {};
    global.navigator = { userAgent: 'node' };
}

/**
 * GLTF文件解析器
 * 负责加载GLTF文件并提取场景层次结构
 */
export class GLTFParser {
    constructor() {
        this.scene = null;
        this.gltf = null;
        this.gltfData = null;
    }

    /**
     * 加载GLTF文件
     * @param {string} filePath - GLTF文件路径
     * @returns {Promise<Object>} 加载的GLTF对象
     */
    async loadGLTF(filePath) {
        try {
            // 检查文件是否存在
            if (!await fs.pathExists(filePath)) {
                throw new Error(`GLTF文件不存在: ${filePath}`);
            }

            console.log(`正在加载GLTF文件: ${filePath}`);

            const ext = path.extname(filePath).toLowerCase();

            if (ext === '.gltf') {
                // 加载JSON格式的GLTF文件
                this.gltfData = await fs.readJSON(filePath);
            } else if (ext === '.glb') {
                // 对于GLB文件，我们需要解析二进制格式
                console.log('检测到GLB文件，正在解析...');
                this.gltfData = await this.parseGLB(filePath);
            } else {
                throw new Error(`不支持的文件格式: ${ext}`);
            }

            // 创建Three.js场景结构
            this.scene = this.createSceneFromGLTF(this.gltfData);

            console.log('GLTF文件加载成功');
            console.log(`场景包含 ${this.scene.children.length} 个根节点`);

            return { scene: this.scene, gltfData: this.gltfData };

        } catch (error) {
            console.error('加载GLTF文件时发生错误:', error);
            throw error;
        }
    }

    /**
     * 解析GLB文件
     * @param {string} filePath - GLB文件路径
     * @returns {Promise<Object>} GLTF数据
     */
    async parseGLB(filePath) {
        const buffer = await fs.readFile(filePath);

        // GLB文件格式：
        // 12字节头部 + JSON chunk + 可选的二进制chunk
        const magic = buffer.readUInt32LE(0);
        const version = buffer.readUInt32LE(4);
        const length = buffer.readUInt32LE(8);

        if (magic !== 0x46546C67) { // "glTF"
            throw new Error('不是有效的GLB文件');
        }

        if (version !== 2) {
            throw new Error(`不支持的GLB版本: ${version}`);
        }

        // 读取JSON chunk
        const jsonChunkLength = buffer.readUInt32LE(12);
        const jsonChunkType = buffer.readUInt32LE(16);

        if (jsonChunkType !== 0x4E4F534A) { // "JSON"
            throw new Error('GLB文件格式错误：缺少JSON chunk');
        }

        const jsonData = buffer.slice(20, 20 + jsonChunkLength);
        const gltfData = JSON.parse(jsonData.toString('utf8'));

        console.log('GLB文件解析成功');
        return gltfData;
    }

    /**
     * 从GLTF数据创建Three.js场景
     * @param {Object} gltfData - GLTF数据
     * @returns {THREE.Object3D} Three.js场景对象
     */
    createSceneFromGLTF(gltfData) {
        const scene = new THREE.Group();
        scene.name = 'Scene Root';
        scene.type = 'Scene';

        if (!gltfData.scenes || !gltfData.nodes) {
            console.warn('GLTF文件缺少场景或节点数据');
            return scene;
        }

        // 获取默认场景
        const sceneIndex = gltfData.scene || 0;
        const sceneData = gltfData.scenes[sceneIndex];

        if (sceneData && sceneData.nodes) {
            // 创建节点层次结构
            sceneData.nodes.forEach(nodeIndex => {
                const node = this.createNodeFromGLTF(gltfData, nodeIndex);
                if (node) {
                    scene.add(node);
                }
            });
        }

        return scene;
    }

    /**
     * 从GLTF数据创建节点
     * @param {Object} gltfData - GLTF数据
     * @param {number} nodeIndex - 节点索引
     * @returns {THREE.Object3D} Three.js节点对象
     */
    createNodeFromGLTF(gltfData, nodeIndex) {
        if (!gltfData.nodes || nodeIndex >= gltfData.nodes.length) {
            return null;
        }

        const nodeData = gltfData.nodes[nodeIndex];
        let node;

        if (nodeData.mesh !== undefined) {
            // 创建网格节点
            node = new THREE.Mesh();
            node.type = 'Mesh';

            // 添加简单的几何体信息
            if (gltfData.meshes && gltfData.meshes[nodeData.mesh]) {
                const meshData = gltfData.meshes[nodeData.mesh];
                node.userData.meshData = meshData;

                // 创建简单的几何体占位符
                const geometry = new THREE.BoxGeometry(1, 1, 1);
                const material = new THREE.MeshBasicMaterial({ color: 0x888888 });
                node.geometry = geometry;
                node.material = material;
            }
        } else {
            // 创建组节点
            node = new THREE.Group();
            node.type = 'Group';
        }

        // 设置节点属性
        node.name = nodeData.name || `Node_${nodeIndex}`;
        node.userData.nodeIndex = nodeIndex;
        node.userData.originalData = nodeData;

        // 设置变换
        if (nodeData.translation) {
            node.position.set(nodeData.translation[0], nodeData.translation[1], nodeData.translation[2]);
        }
        if (nodeData.rotation) {
            node.quaternion.set(nodeData.rotation[0], nodeData.rotation[1], nodeData.rotation[2], nodeData.rotation[3]);
        }
        if (nodeData.scale) {
            node.scale.set(nodeData.scale[0], nodeData.scale[1], nodeData.scale[2]);
        }
        if (nodeData.matrix) {
            const matrix = new THREE.Matrix4();
            matrix.fromArray(nodeData.matrix);
            matrix.decompose(node.position, node.quaternion, node.scale);
        }

        // 递归创建子节点
        if (nodeData.children) {
            nodeData.children.forEach(childIndex => {
                const childNode = this.createNodeFromGLTF(gltfData, childIndex);
                if (childNode) {
                    node.add(childNode);
                }
            });
        }

        return node;
    }

    /**
     * 获取场景根节点
     * @returns {THREE.Object3D} 场景根节点
     */
    getScene() {
        if (!this.scene) {
            throw new Error('请先加载GLTF文件');
        }
        return this.scene;
    }

    /**
     * 获取完整的GLTF对象
     * @returns {Object} GLTF对象
     */
    getGLTF() {
        if (!this.gltf) {
            throw new Error('请先加载GLTF文件');
        }
        return this.gltf;
    }

    /**
     * 递归遍历场景节点
     * @param {THREE.Object3D} node - 当前节点
     * @param {Function} callback - 回调函数
     * @param {number} depth - 当前深度
     */
    traverseNodes(node, callback, depth = 0) {
        callback(node, depth);
        
        if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
                this.traverseNodes(child, callback, depth + 1);
            });
        }
    }

    /**
     * 获取节点的基本信息
     * @param {THREE.Object3D} node - 节点对象
     * @returns {Object} 节点信息
     */
    getNodeInfo(node) {
        const info = {
            name: node.name || 'Unnamed',
            type: node.type,
            uuid: node.uuid,
            visible: node.visible,
            position: {
                x: node.position.x,
                y: node.position.y,
                z: node.position.z
            },
            rotation: {
                x: node.rotation.x,
                y: node.rotation.y,
                z: node.rotation.z
            },
            scale: {
                x: node.scale.x,
                y: node.scale.y,
                z: node.scale.z
            },
            childrenCount: node.children ? node.children.length : 0,
            hasMesh: node.type === 'Mesh',
            hasGeometry: node.geometry !== undefined,
            hasMaterial: node.material !== undefined
        };

        // 如果是网格对象，添加几何体和材质信息
        if (node.type === 'Mesh' && node.geometry) {
            info.geometry = {
                type: node.geometry.type,
                verticesCount: node.geometry.attributes.position ? 
                    node.geometry.attributes.position.count : 0
            };
        }

        if (node.material) {
            info.material = {
                type: node.material.type,
                name: node.material.name || 'Unnamed Material'
            };
        }

        return info;
    }

    /**
     * 获取场景统计信息
     * @returns {Object} 统计信息
     */
    getSceneStats() {
        if (!this.scene) {
            throw new Error('请先加载GLTF文件');
        }

        const stats = {
            totalNodes: 0,
            meshNodes: 0,
            groupNodes: 0,
            lightNodes: 0,
            cameraNodes: 0,
            maxDepth: 0,
            nodesByType: {}
        };

        this.traverseNodes(this.scene, (node, depth) => {
            stats.totalNodes++;
            stats.maxDepth = Math.max(stats.maxDepth, depth);

            // 按类型统计
            if (!stats.nodesByType[node.type]) {
                stats.nodesByType[node.type] = 0;
            }
            stats.nodesByType[node.type]++;

            // 特定类型统计
            switch (node.type) {
                case 'Mesh':
                    stats.meshNodes++;
                    break;
                case 'Group':
                    stats.groupNodes++;
                    break;
                case 'Light':
                case 'DirectionalLight':
                case 'PointLight':
                case 'SpotLight':
                    stats.lightNodes++;
                    break;
                case 'Camera':
                case 'PerspectiveCamera':
                case 'OrthographicCamera':
                    stats.cameraNodes++;
                    break;
            }
        });

        return stats;
    }

    /**
     * 查找指定名称的节点
     * @param {string} name - 节点名称
     * @returns {THREE.Object3D[]} 匹配的节点数组
     */
    findNodesByName(name) {
        if (!this.scene) {
            throw new Error('请先加载GLTF文件');
        }

        const foundNodes = [];
        this.traverseNodes(this.scene, (node) => {
            if (node.name === name) {
                foundNodes.push(node);
            }
        });

        return foundNodes;
    }

    /**
     * 查找指定类型的节点
     * @param {string} type - 节点类型
     * @returns {THREE.Object3D[]} 匹配的节点数组
     */
    findNodesByType(type) {
        if (!this.scene) {
            throw new Error('请先加载GLTF文件');
        }

        const foundNodes = [];
        this.traverseNodes(this.scene, (node) => {
            if (node.type === type) {
                foundNodes.push(node);
            }
        });

        return foundNodes;
    }
}
